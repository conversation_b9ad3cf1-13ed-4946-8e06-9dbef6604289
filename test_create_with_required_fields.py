#!/usr/bin/env python3
"""
Test create operation with required fields
"""

import asyncio

# Real Odoo server credentials
ODOO_HOST = "https://odoo18.bestmix.one/"
ODOO_DATABASE = "bestmix_27_6"
ODOO_USERNAME = "tuan.le"
ODOO_PASSWORD = "drb6mtw3bah8byu*VEV"

async def test_create_with_required_fields():
    """Test create with all required fields."""
    print("🧪 Testing create with required fields...")
    
    try:
        from zenoo_rpc import ZenooClient
        
        async with ZenooClient(ODOO_HOST) as client:
            await client.login(ODOO_DATABASE, ODOO_USERNAME, ODOO_PASSWORD)
            print("✅ Authentication successful")
            
            # Test 1: Create with autopost_bills field (required)
            print("\n📝 Test 1: Create with autopost_bills")
            try:
                partner_id = await client.create("res.partner", {
                    "name": "Test Partner with Required Fields",
                    "autopost_bills": "no"  # Required field
                })
                print(f"✅ Partner created with ID: {partner_id}")
                
                # Clean up
                await client.unlink("res.partner", [partner_id])
                print("✅ Partner deleted successfully")
                
            except Exception as e:
                print(f"❌ Create with autopost_bills failed: {e}")
            
            # Test 2: Try different autopost_bills values
            print("\n📝 Test 2: Try different autopost_bills values")
            
            # First, let's check what values are allowed for autopost_bills
            try:
                fields_info = await client.execute_kw(
                    "res.partner", 
                    "fields_get", 
                    [], 
                    {"attributes": ["selection"]}
                )
                
                autopost_bills_info = fields_info.get("autopost_bills", {})
                selection_values = autopost_bills_info.get("selection", [])
                print(f"📋 autopost_bills selection values: {selection_values}")
                
                if selection_values:
                    # Try with first valid value
                    first_value = selection_values[0][0]  # [('value', 'label'), ...]
                    print(f"🔄 Trying with value: {first_value}")
                    
                    partner_id = await client.create("res.partner", {
                        "name": "Test Partner Selection Value",
                        "autopost_bills": first_value
                    })
                    print(f"✅ Partner created with selection value: {partner_id}")
                    
                    # Clean up
                    await client.unlink("res.partner", [partner_id])
                    print("✅ Partner deleted successfully")
                
            except Exception as e:
                print(f"❌ Selection value test failed: {e}")
            
            # Test 3: Try using QueryBuilder.create with required fields
            print("\n📝 Test 3: QueryBuilder.create with required fields")
            try:
                from zenoo_rpc.models.common import ResPartner
                
                # Get valid selection value first
                fields_info = await client.execute_kw(
                    "res.partner", 
                    "fields_get", 
                    [], 
                    {"attributes": ["selection"]}
                )
                
                autopost_bills_info = fields_info.get("autopost_bills", {})
                selection_values = autopost_bills_info.get("selection", [])
                
                if selection_values:
                    first_value = selection_values[0][0]
                    
                    partner = await client.model(ResPartner).create(
                        name="QueryBuilder Test Partner",
                        autopost_bills=first_value
                    )
                    print(f"✅ QueryBuilder create successful: {partner.id}")
                    
                    # Clean up using client.unlink
                    await client.unlink("res.partner", [partner.id])
                    print("✅ QueryBuilder partner deleted successfully")
                
            except Exception as e:
                print(f"❌ QueryBuilder create failed: {e}")
                import traceback
                traceback.print_exc()
            
            # Test 4: Check if we can create other types of records
            print("\n📝 Test 4: Try creating a simple record (res.country.state)")
            try:
                # Try creating a country state (usually simpler)
                state_id = await client.create("res.country.state", {
                    "name": "Test State",
                    "code": "TS",
                    "country_id": 241  # Vietnam ID from earlier tests
                })
                print(f"✅ State created with ID: {state_id}")
                
                # Clean up
                await client.unlink("res.country.state", [state_id])
                print("✅ State deleted successfully")
                
            except Exception as e:
                print(f"❌ State creation failed: {e}")
                
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Run the test."""
    print("🚀 Testing create operations with required fields...\n")
    await test_create_with_required_fields()
    print("\n🏁 Test completed")

if __name__ == "__main__":
    asyncio.run(main())
