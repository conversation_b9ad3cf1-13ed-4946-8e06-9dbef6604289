#!/usr/bin/env python3
"""
Test Zenoo RPC 0.1.1 with real Odoo server
Testing all examples from README.md with correct API
"""

import asyncio
import sys
from typing import List, Optional
from zenoo_rpc import ZenooClient
from zenoo_rpc.models import OdooModel

# Test server configuration
ODOO_CONFIG = {
    "url": "https://odoo18.bestmix.one",
    "database": "bestmix_27_6", 
    "username": "tuan.le",
    "password": "drb6mtw3bah8byu*VEV"
}

# Define models as in README
class ResPartner(OdooModel):
    """Partner/Customer model"""
    _odoo_model = "res.partner"
    
    name: str
    email: Optional[str] = None
    phone: Optional[str] = None
    is_company: bool = False
    customer_rank: int = 0
    supplier_rank: int = 0

async def test_basic_connection():
    """Test 1: Basic connection and authentication"""
    print("🔗 Test 1: Basic Connection")
    try:
        async with ZenooClient(ODOO_CONFIG["url"]) as client:
            await client.login(
                database=ODOO_CONFIG["database"],
                username=ODOO_CONFIG["username"],
                password=ODOO_CONFIG["password"]
            )
            
            # Test basic info
            user_info = await client.execute("res.users", "read", [client.uid], ["name", "login"])
            print(f"✅ Connected as: {user_info[0]['name']} ({user_info[0]['login']})")
            return True
            
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

async def test_crud_operations():
    """Test 2: CRUD Operations"""
    print("\n📝 Test 2: CRUD Operations")
    try:
        async with ZenooClient(ODOO_CONFIG["url"]) as client:
            await client.login(
                database=ODOO_CONFIG["database"],
                username=ODOO_CONFIG["username"],
                password=ODOO_CONFIG["password"]
            )
            
            # Create a test partner
            partner_data = {
                "name": "Zenoo Test Partner",
                "email": "<EMAIL>",
                "phone": "+1234567890",
                "is_company": False,
                "customer_rank": 1
            }
            
            partner_id = await client.create("res.partner", partner_data)
            print(f"✅ Created partner with ID: {partner_id}")
            
            # Read the partner
            partner = await client.read("res.partner", [partner_id], ["name", "email", "phone"])
            print(f"✅ Read partner: {partner[0]['name']}")
            
            # Update the partner
            await client.write("res.partner", [partner_id], {"phone": "+0987654321"})
            print("✅ Updated partner phone")
            
            # Search for partners
            partner_ids = await client.search("res.partner", [["name", "=", "Zenoo Test Partner"]])
            print(f"✅ Found {len(partner_ids)} partner(s)")
            
            # Delete the test partner
            await client.unlink("res.partner", [partner_id])
            print("✅ Deleted test partner")
            
            return True
            
    except Exception as e:
        print(f"❌ CRUD operations failed: {e}")
        return False

async def test_search_and_filtering():
    """Test 3: Advanced Search and Filtering"""
    print("\n🔍 Test 3: Advanced Search and Filtering")
    try:
        async with ZenooClient(ODOO_CONFIG["url"]) as client:
            await client.login(
                database=ODOO_CONFIG["database"],
                username=ODOO_CONFIG["username"],
                password=ODOO_CONFIG["password"]
            )
            
            # Search customers
            customer_ids = await client.search("res.partner", [
                ["customer_rank", ">", 0],
                ["is_company", "=", True]
            ], limit=5)
            print(f"✅ Found {len(customer_ids)} company customers")
            
            # Search and read in one call
            customers = await client.search_read("res.partner", [
                ["customer_rank", ">", 0]
            ], ["name", "email", "phone"], limit=3)
            print(f"✅ Search and read {len(customers)} customers")
            
            # Count records
            total_partners = await client.search_count("res.partner", [])
            print(f"✅ Total partners in system: {total_partners}")
            
            return True
            
    except Exception as e:
        print(f"❌ Search and filtering failed: {e}")
        return False

async def test_pydantic_models():
    """Test 4: Pydantic Models"""
    print("\n🏗️ Test 4: Pydantic Models")
    try:
        async with ZenooClient(ODOO_CONFIG["url"]) as client:
            await client.login(
                database=ODOO_CONFIG["database"],
                username=ODOO_CONFIG["username"],
                password=ODOO_CONFIG["password"]
            )
            
            # Search using model
            partners = await ResPartner.search(client, [["customer_rank", ">", 0]], limit=5)
            print(f"✅ Found {len(partners)} customers using Pydantic model")
            
            # Create partner using Pydantic model
            partner = ResPartner(
                name="Zenoo Pydantic Partner",
                email="<EMAIL>",
                is_company=False,
                customer_rank=1
            )
            
            # Save to Odoo
            saved_partner = await partner.save(client)
            print(f"✅ Created partner with Pydantic model: {saved_partner.id}")
            
            # Update using model
            saved_partner.phone = "+1111111111"
            await saved_partner.update(client)
            print("✅ Updated partner using Pydantic model")
            
            # Delete using model
            await saved_partner.delete(client)
            print("✅ Deleted partner using Pydantic model")
            
            return True
            
    except Exception as e:
        print(f"❌ Pydantic models failed: {e}")
        return False

async def test_error_handling():
    """Test 5: Error Handling"""
    print("\n⚠️ Test 5: Error Handling")
    try:
        async with ZenooClient(ODOO_CONFIG["url"]) as client:
            await client.login(
                database=ODOO_CONFIG["database"],
                username=ODOO_CONFIG["username"],
                password=ODOO_CONFIG["password"]
            )
            
            # Test invalid model
            try:
                await client.search("invalid.model", [])
                print("❌ Should have failed with invalid model")
                return False
            except Exception as e:
                print(f"✅ Correctly caught error for invalid model: {type(e).__name__}")
            
            # Test invalid field
            try:
                await client.read("res.partner", [1], ["invalid_field"])
                print("❌ Should have failed with invalid field")
                return False
            except Exception as e:
                print(f"✅ Correctly caught error for invalid field: {type(e).__name__}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False

async def run_all_tests():
    """Run all tests"""
    print("🚀 Testing Zenoo RPC 0.1.1 with Real Odoo Server")
    print("=" * 60)
    
    tests = [
        test_basic_connection,
        test_crud_operations,
        test_search_and_filtering,
        test_pydantic_models,
        test_error_handling
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {test.__name__}: {status}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Zenoo RPC 0.1.1 is working perfectly!")
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = asyncio.run(run_all_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
