#!/usr/bin/env python3
"""
Test script for README.md examples with real Odoo server
"""

import asyncio
import sys
import traceback

# Real Odoo server credentials
ODOO_HOST = "https://odoo18.bestmix.one/"
ODOO_DATABASE = "bestmix_27_6"
ODOO_USERNAME = "tuan.le"
ODOO_PASSWORD = "drb6mtw3bah8byu*VEV"

async def test_basic_connection():
    """Test basic connection to real Odoo server."""
    print("🧪 Testing basic connection to real Odoo server...")
    
    try:
        from zenoo_rpc import ZenooClient
        
        # Test connection and authentication
        async with ZenooClient(ODOO_HOST) as client:
            print("✅ Client created successfully")
            
            # Test login
            await client.login(ODOO_DATABASE, ODOO_USERNAME, ODOO_PASSWORD)
            print("✅ Authentication successful")
            
            return True
            
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        traceback.print_exc()
        return False

async def test_model_query():
    """Test model query with real data."""
    print("\n🧪 Testing model query with real data...")
    
    try:
        from zenoo_rpc import ZenooClient
        from zenoo_rpc.models.common import ResPartner
        
        async with ZenooClient(ODOO_HOST) as client:
            await client.login(ODOO_DATABASE, ODOO_USERNAME, ODOO_PASSWORD)
            print("✅ Authenticated successfully")
            
            # Test model query
            partners = await client.model(ResPartner).all().limit(5).all()
            print(f"✅ Retrieved {len(partners)} partners")
            
            # Test accessing partner data
            if partners:
                first_partner = partners[0]
                print(f"✅ First partner: ID={first_partner.id}, Name={first_partner.name}")
            
            return True
            
    except Exception as e:
        print(f"❌ Model query failed: {e}")
        traceback.print_exc()
        return False

async def test_filtered_query():
    """Test filtered query."""
    print("\n🧪 Testing filtered query...")
    
    try:
        from zenoo_rpc import ZenooClient
        from zenoo_rpc.models.common import ResPartner
        
        async with ZenooClient(ODOO_HOST) as client:
            await client.login(ODOO_DATABASE, ODOO_USERNAME, ODOO_PASSWORD)
            
            # Test filtered query
            companies = await client.model(ResPartner).filter(
                is_company=True
            ).limit(3).all()
            
            print(f"✅ Retrieved {len(companies)} companies")
            
            for company in companies:
                print(f"  - Company: {company.name} (ID: {company.id})")
            
            return True
            
    except Exception as e:
        print(f"❌ Filtered query failed: {e}")
        traceback.print_exc()
        return False

async def test_create_and_delete():
    """Test create and delete operations."""
    print("\n🧪 Testing create and delete operations...")
    
    try:
        from zenoo_rpc import ZenooClient
        from zenoo_rpc.models.common import ResPartner
        
        async with ZenooClient(ODOO_HOST) as client:
            await client.login(ODOO_DATABASE, ODOO_USERNAME, ODOO_PASSWORD)
            
            # Create a test partner
            test_partner = await client.model(ResPartner).create(
                name="Zenoo RPC Test Partner",
                email="<EMAIL>",
                is_company=False
            )
            
            print(f"✅ Created test partner: ID={test_partner.id}, Name={test_partner.name}")
            
            # Update the partner using client.write
            await client.write(
                "res.partner",
                [test_partner.id],
                {"phone": "+1234567890"}
            )

            print("✅ Updated partner phone")

            # Delete the test partner using client.unlink
            await client.unlink("res.partner", [test_partner.id])
            print("✅ Deleted test partner successfully")
            
            return True
            
    except Exception as e:
        print(f"❌ Create/delete operations failed: {e}")
        traceback.print_exc()
        return False

async def test_transaction():
    """Test transaction functionality."""
    print("\n🧪 Testing transaction functionality...")
    
    try:
        from zenoo_rpc import ZenooClient
        from zenoo_rpc.models.common import ResPartner
        
        async with ZenooClient(ODOO_HOST) as client:
            await client.login(ODOO_DATABASE, ODOO_USERNAME, ODOO_PASSWORD)
            
            # Test if transaction method exists
            if hasattr(client, 'transaction'):
                print("✅ Transaction method exists")
                
                # Test transaction context (without actual operations)
                try:
                    async with client.transaction() as tx:
                        print("✅ Transaction context works")
                        # Don't create actual data in transaction test
                        
                except Exception as tx_error:
                    print(f"⚠️  Transaction context failed: {tx_error}")
                    # This might be expected if transaction is not fully implemented
                    
            else:
                print("❌ Transaction method not found")
                return False
            
            return True
            
    except Exception as e:
        print(f"❌ Transaction test failed: {e}")
        traceback.print_exc()
        return False

async def test_batch_operations():
    """Test batch operations."""
    print("\n🧪 Testing batch operations...")
    
    try:
        from zenoo_rpc import ZenooClient
        from zenoo_rpc.models.common import ResPartner
        
        async with ZenooClient(ODOO_HOST) as client:
            await client.login(ODOO_DATABASE, ODOO_USERNAME, ODOO_PASSWORD)
            
            # Test if batch method exists
            if hasattr(client, 'batch'):
                print("✅ Batch method exists")
                
                # Test batch context (without actual operations)
                try:
                    async with client.batch() as batch:
                        print("✅ Batch context works")
                        # Don't create actual data in batch test
                        
                except Exception as batch_error:
                    print(f"⚠️  Batch context failed: {batch_error}")
                    # This might be expected if batch is not fully implemented
                    
            else:
                print("❌ Batch method not found")
                return False
            
            return True
            
    except Exception as e:
        print(f"❌ Batch operations test failed: {e}")
        traceback.print_exc()
        return False

async def test_readme_example():
    """Test the exact example from README.md."""
    print("\n🧪 Testing README.md example...")
    
    try:
        from zenoo_rpc import ZenooClient
        from zenoo_rpc.models.common import ResPartner
        
        # This is the exact example from README.md
        async with ZenooClient(ODOO_HOST) as client:
            # Authenticate
            await client.login(ODOO_DATABASE, ODOO_USERNAME, ODOO_PASSWORD)
            print("✅ Authentication successful")
            
            # Type-safe queries with IDE support
            partners = await client.model(ResPartner).filter(
                is_company=True,
                name__ilike="company%"
            ).limit(10).all()
            
            print(f"✅ Retrieved {len(partners)} partners matching criteria")
            
            # Access fields with full type safety
            for partner in partners[:3]:  # Show only first 3
                print(f"  - Company: {partner.name} - Email: {partner.email}")
            
            return True
            
    except Exception as e:
        print(f"❌ README example failed: {e}")
        traceback.print_exc()
        return False

async def main():
    """Run all tests with real Odoo server."""
    print("🚀 Testing Zenoo RPC with real Odoo server...")
    print(f"🌐 Server: {ODOO_HOST}")
    print(f"🗄️  Database: {ODOO_DATABASE}")
    print(f"👤 User: {ODOO_USERNAME}")
    print("=" * 60)
    
    tests = [
        ("Basic Connection", test_basic_connection),
        ("Model Query", test_model_query),
        ("Filtered Query", test_filtered_query),
        ("Create and Delete", test_create_and_delete),
        ("Transaction", test_transaction),
        ("Batch Operations", test_batch_operations),
        ("README Example", test_readme_example),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("📊 REAL ODOO SERVER TEST RESULTS")
    print("="*60)
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n📈 Total: {len(results)} tests")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    
    if failed > 0:
        print(f"\n⚠️  {failed} tests failed. Some functionality needs fixes!")
        return 1
    else:
        print(f"\n🎉 All tests passed! Zenoo RPC works with real Odoo server!")
        return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
