#!/usr/bin/env python3
"""
Test create operation with proper business rule compliance
"""

import asyncio

# Real Odoo server credentials
ODOO_HOST = "https://odoo18.bestmix.one/"
ODOO_DATABASE = "bestmix_27_6"
ODOO_USERNAME = "tuan.le"
ODOO_PASSWORD = "drb6mtw3bah8byu*VEV"

async def test_create_with_business_rules():
    """Test create with proper business rule compliance."""
    print("🧪 Testing create with business rule compliance...")
    
    try:
        from zenoo_rpc import ZenooClient
        from zenoo_rpc.models.common import ResPartner
        
        async with ZenooClient(ODOO_HOST) as client:
            await client.login(ODOO_DATABASE, ODOO_USERNAME, ODOO_PASSWORD)
            print("✅ Authentication successful")
            
            # Test 1: Create partner with phone (satisfies business rule)
            print("\n📝 Test 1: Create partner with phone")
            try:
                partner_id = await client.create("res.partner", {
                    "name": "Test Partner with Phone",
                    "phone": "+***********",  # Satisfies business rule
                    "autopost_bills": "always"  # Required field
                })
                print(f"✅ Partner created with phone: ID {partner_id}")
                
                # Clean up
                await client.unlink("res.partner", [partner_id])
                print("✅ Partner deleted successfully")
                
            except Exception as e:
                print(f"❌ Create with phone failed: {e}")
            
            # Test 2: Create partner with mobile
            print("\n📝 Test 2: Create partner with mobile")
            try:
                partner_id = await client.create("res.partner", {
                    "name": "Test Partner with Mobile",
                    "mobile": "+***********",  # Satisfies business rule
                    "autopost_bills": "always"
                })
                print(f"✅ Partner created with mobile: ID {partner_id}")
                
                # Clean up
                await client.unlink("res.partner", [partner_id])
                print("✅ Partner deleted successfully")
                
            except Exception as e:
                print(f"❌ Create with mobile failed: {e}")
            
            # Test 3: Create partner with VAT (tax ID)
            print("\n📝 Test 3: Create partner with VAT")
            try:
                partner_id = await client.create("res.partner", {
                    "name": "Test Partner with VAT",
                    "vat": "1234567890",  # Satisfies business rule
                    "autopost_bills": "always"
                })
                print(f"✅ Partner created with VAT: ID {partner_id}")
                
                # Clean up
                await client.unlink("res.partner", [partner_id])
                print("✅ Partner deleted successfully")
                
            except Exception as e:
                print(f"❌ Create with VAT failed: {e}")
            
            # Test 4: QueryBuilder.create with proper fields
            print("\n📝 Test 4: QueryBuilder.create with proper fields")
            try:
                partner = await client.model(ResPartner).create(
                    name="QueryBuilder Test Partner",
                    phone="+***********",  # Satisfies business rule
                    autopost_bills="always"  # Required field
                )
                print(f"✅ QueryBuilder create successful: ID {partner.id}, Name: {partner.name}")
                
                # Test accessing fields
                print(f"📋 Partner details: Phone={partner.phone}, Name={partner.name}")
                
                # Clean up using client.unlink
                await client.unlink("res.partner", [partner.id])
                print("✅ QueryBuilder partner deleted successfully")
                
            except Exception as e:
                print(f"❌ QueryBuilder create failed: {e}")
                import traceback
                traceback.print_exc()
            
            # Test 5: Create with all three fields (comprehensive)
            print("\n📝 Test 5: Create with comprehensive fields")
            try:
                partner = await client.model(ResPartner).create(
                    name="Comprehensive Test Partner",
                    phone="+***********",
                    mobile="+84999888777", 
                    vat="9876543210",
                    email="<EMAIL>",
                    autopost_bills="always",
                    is_company=False
                )
                print(f"✅ Comprehensive create successful: ID {partner.id}")
                print(f"📋 Details: Name={partner.name}, Phone={partner.phone}, Mobile={partner.mobile}")
                
                # Clean up
                await client.unlink("res.partner", [partner.id])
                print("✅ Comprehensive partner deleted successfully")
                
            except Exception as e:
                print(f"❌ Comprehensive create failed: {e}")
                import traceback
                traceback.print_exc()
                
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Run the test."""
    print("🚀 Testing create operations with business rule compliance...\n")
    await test_create_with_business_rules()
    print("\n🏁 Test completed")

if __name__ == "__main__":
    asyncio.run(main())
