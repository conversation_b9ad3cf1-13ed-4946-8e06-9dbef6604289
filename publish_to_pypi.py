#!/usr/bin/env python3
"""
Script to publish Zenoo RPC to PyPI

This script will:
1. Verify the package is ready for publishing
2. Upload to Test PyPI first (optional)
3. Upload to Production PyPI after confirmation
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(cmd, check=True):
    """Run a shell command and return the result."""
    print(f"🔄 Running: {cmd}")
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    if result.stdout:
        print(result.stdout)
    if result.stderr:
        print(result.stderr)
    
    if check and result.returncode != 0:
        print(f"❌ Command failed with return code {result.returncode}")
        sys.exit(1)
    
    return result

def verify_package():
    """Verify the package is ready for publishing."""
    print("🔍 Verifying package...")
    
    # Check if dist files exist
    dist_files = list(Path("dist").glob("zenoo_rpc-0.1.1*"))
    if not dist_files:
        print("❌ No distribution files found. Run 'python -m build' first.")
        sys.exit(1)
    
    print(f"✅ Found distribution files: {[f.name for f in dist_files]}")
    
    # Check package with twine (ignore license-file warning)
    print("🔍 Checking package with twine...")
    result = run_command("python -m twine check dist/*", check=False)
    
    # We expect the license-file warning, so we'll continue if that's the only issue
    if result.returncode != 0:
        if "license-file" in result.stderr:
            print("⚠️  License-file warning detected (this is expected and won't affect functionality)")
        else:
            print("❌ Package check failed with unexpected errors")
            sys.exit(1)
    
    print("✅ Package verification completed")

def test_import():
    """Test that the package can be imported."""
    print("🧪 Testing package import...")
    
    result = run_command(
        'python -c "import zenoo_rpc; print(f\'Version: {zenoo_rpc.__version__}\')"',
        check=False
    )
    
    if result.returncode != 0:
        print("❌ Package import failed")
        sys.exit(1)
    
    if "0.1.1" not in result.stdout:
        print("❌ Version mismatch in imported package")
        sys.exit(1)
    
    print("✅ Package import test passed")

def publish_to_test_pypi():
    """Publish to Test PyPI."""
    print("📦 Publishing to Test PyPI...")
    
    confirm = input("Do you want to publish to Test PyPI first? (y/N): ").strip().lower()
    if confirm != 'y':
        print("⏭️  Skipping Test PyPI")
        return
    
    # Check if we have test pypi credentials
    print("🔑 Make sure you have Test PyPI credentials configured:")
    print("   pip install twine")
    print("   python -m twine upload --repository testpypi dist/*")
    print()
    
    confirm = input("Ready to upload to Test PyPI? (y/N): ").strip().lower()
    if confirm != 'y':
        print("⏭️  Skipping Test PyPI upload")
        return
    
    run_command("python -m twine upload --repository testpypi dist/*")
    print("✅ Successfully published to Test PyPI")
    
    print("\n🧪 You can test the package from Test PyPI with:")
    print("   pip install --index-url https://test.pypi.org/simple/ zenoo-rpc==0.1.1")

def publish_to_pypi():
    """Publish to Production PyPI."""
    print("\n" + "="*60)
    print("🚀 READY TO PUBLISH TO PRODUCTION PYPI")
    print("="*60)
    
    print("📋 Pre-publish checklist:")
    print("✅ Package built successfully")
    print("✅ Package verified with twine")
    print("✅ Import test passed")
    print("✅ Version updated to 0.1.1")
    print("✅ CHANGELOG.md updated")
    print("✅ All fixes implemented and tested")
    
    print("\n⚠️  WARNING: This will publish to PRODUCTION PyPI!")
    print("   Once published, this version cannot be deleted or modified.")
    print("   Make sure everything is correct.")
    
    confirm = input("\nDo you want to publish to Production PyPI? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ Publication cancelled by user")
        return False
    
    # Final confirmation
    print("\n🔴 FINAL CONFIRMATION")
    print("This will publish zenoo-rpc version 0.1.1 to PyPI.")
    final_confirm = input("Type 'PUBLISH' to confirm: ").strip()
    
    if final_confirm != 'PUBLISH':
        print("❌ Publication cancelled - confirmation text didn't match")
        return False
    
    print("\n🚀 Publishing to Production PyPI...")
    run_command("python -m twine upload dist/*")
    
    print("\n🎉 SUCCESS! Zenoo RPC 0.1.1 has been published to PyPI!")
    print("\n📦 Users can now install it with:")
    print("   pip install zenoo-rpc==0.1.1")
    print("   pip install --upgrade zenoo-rpc")
    
    return True

def main():
    """Main function."""
    print("🚀 Zenoo RPC PyPI Publishing Script")
    print("="*50)
    
    # Change to project directory
    os.chdir(Path(__file__).parent)
    
    # Verify package
    verify_package()
    
    # Test import
    test_import()
    
    # Optional: Publish to Test PyPI
    publish_to_test_pypi()
    
    # Publish to Production PyPI
    success = publish_to_pypi()
    
    if success:
        print("\n🎯 Next steps:")
        print("1. Update GitHub repository with new version")
        print("2. Create a GitHub release for v0.1.1")
        print("3. Update documentation if needed")
        print("4. Announce the release")
        
        print("\n📊 Package stats:")
        print("   PyPI: https://pypi.org/project/zenoo-rpc/")
        print("   Downloads: https://pypistats.org/packages/zenoo-rpc")
    
    print("\n✨ Publishing process completed!")

if __name__ == "__main__":
    main()
