#!/usr/bin/env python3
"""
Simple test for Zenoo RPC 0.1.1 with available methods only
"""

import asyncio
import sys
from zenoo_rpc import ZenooClient

# Test server configuration
ODOO_CONFIG = {
    "url": "https://odoo18.bestmix.one",
    "database": "bestmix_27_6", 
    "username": "tuan.le",
    "password": "drb6mtw3bah8byu*VEV"
}

async def test_basic_connection():
    """Test 1: Basic connection and authentication"""
    print("🔗 Test 1: Basic Connection")
    try:
        async with ZenooClient(ODOO_CONFIG["url"]) as client:
            await client.login(
                database=ODOO_CONFIG["database"],
                username=ODOO_CONFIG["username"],
                password=ODOO_CONFIG["password"]
            )
            
            # Test basic info
            user_info = await client.execute("res.users", "read", [client.uid], ["name", "login"])
            print(f"✅ Connected as: {user_info[0]['name']} ({user_info[0]['login']})")
            print(f"✅ User ID: {client.uid}")
            print(f"✅ Database: {client.database}")
            return True
            
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

async def test_crud_operations():
    """Test 2: CRUD Operations with available methods"""
    print("\n📝 Test 2: CRUD Operations")
    try:
        async with ZenooClient(ODOO_CONFIG["url"]) as client:
            await client.login(
                database=ODOO_CONFIG["database"],
                username=ODOO_CONFIG["username"],
                password=ODOO_CONFIG["password"]
            )
            
            # Create a test partner
            partner_data = {
                "name": "Zenoo Test Partner v0.1.1",
                "email": "<EMAIL>",
                "is_company": False
            }
            
            partner_id = await client.create("res.partner", partner_data)
            print(f"✅ Created partner with ID: {partner_id}")
            
            # Read the partner
            partner = await client.read("res.partner", [partner_id], ["name", "email"])
            print(f"✅ Read partner: {partner[0]['name']}")
            
            # Update the partner
            await client.write("res.partner", [partner_id], {"email": "<EMAIL>"})
            print("✅ Updated partner email")
            
            # Delete the test partner
            await client.unlink("res.partner", [partner_id])
            print("✅ Deleted test partner")
            
            return True
            
    except Exception as e:
        print(f"❌ CRUD operations failed: {e}")
        return False

async def test_search_read():
    """Test 3: Search and Read operations"""
    print("\n🔍 Test 3: Search and Read")
    try:
        async with ZenooClient(ODOO_CONFIG["url"]) as client:
            await client.login(
                database=ODOO_CONFIG["database"],
                username=ODOO_CONFIG["username"],
                password=ODOO_CONFIG["password"]
            )
            
            # Search and read partners
            partners = await client.search_read("res.partner", [
                ["is_company", "=", True]
            ], ["name", "email"], limit=3)
            print(f"✅ Found {len(partners)} company partners")
            
            if partners:
                print(f"   First partner: {partners[0]['name']}")
            
            # Count total partners
            total_partners = await client.search_count("res.partner", [])
            print(f"✅ Total partners in system: {total_partners}")
            
            return True
            
    except Exception as e:
        print(f"❌ Search and read failed: {e}")
        return False

async def test_server_info():
    """Test 4: Server Information"""
    print("\n🖥️ Test 4: Server Information")
    try:
        async with ZenooClient(ODOO_CONFIG["url"]) as client:
            await client.login(
                database=ODOO_CONFIG["database"],
                username=ODOO_CONFIG["username"],
                password=ODOO_CONFIG["password"]
            )
            
            # Get server version
            version = await client.get_server_version()
            print(f"✅ Server version: {version}")
            
            # List databases (if accessible)
            try:
                databases = await client.list_databases()
                print(f"✅ Available databases: {len(databases)} found")
            except Exception as e:
                print(f"⚠️ Cannot list databases (normal for restricted access): {type(e).__name__}")
            
            # Health check
            health = await client.health_check()
            print(f"✅ Health check: {health}")
            
            return True
            
    except Exception as e:
        print(f"❌ Server info failed: {e}")
        return False

async def test_model_operations():
    """Test 5: Model Operations"""
    print("\n🏗️ Test 5: Model Operations")
    try:
        async with ZenooClient(ODOO_CONFIG["url"]) as client:
            await client.login(
                database=ODOO_CONFIG["database"],
                username=ODOO_CONFIG["username"],
                password=ODOO_CONFIG["password"]
            )
            
            # Get model fields
            fields = await client.get_model_fields("res.partner")
            print(f"✅ res.partner has {len(fields)} fields")
            print(f"   Sample fields: {list(fields.keys())[:5]}")
            
            # Check model access
            access = await client.check_model_access("res.partner", "read")
            print(f"✅ Read access to res.partner: {access}")
            
            return True
            
    except Exception as e:
        print(f"❌ Model operations failed: {e}")
        return False

async def run_all_tests():
    """Run all tests"""
    print("🚀 Testing Zenoo RPC 0.1.1 - Available Methods Only")
    print("=" * 60)
    
    tests = [
        test_basic_connection,
        test_crud_operations,
        test_search_read,
        test_server_info,
        test_model_operations
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {test.__name__}: {status}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Zenoo RPC 0.1.1 core functionality works!")
    elif passed > 0:
        print("⚠️ Some tests passed. Core functionality is working.")
    else:
        print("❌ All tests failed. There may be connection or authentication issues.")
    
    return passed >= 3  # Consider success if at least 3 tests pass

if __name__ == "__main__":
    try:
        success = asyncio.run(run_all_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
