#!/usr/bin/env python3
"""
Test script for README.md code examples
"""

import asyncio
import sys
import traceback

def test_imports():
    """Test if all imports work correctly."""
    print("🧪 Testing imports...")
    
    try:
        from zenoo_rpc import ZenooClient
        print("✅ ZenooClient import successful")
    except ImportError as e:
        print(f"❌ ZenooClient import failed: {e}")
        return False
    
    try:
        from zenoo_rpc.models.common import ResPartner
        print("✅ ResPartner import successful")
    except ImportError as e:
        print(f"❌ ResPartner import failed: {e}")
        return False
    
    return True

async def test_basic_client_creation():
    """Test basic client creation and connection."""
    print("\n🧪 Testing basic client creation...")

    try:
        from zenoo_rpc import ZenooClient

        # Test client creation (without actual connection)
        client = ZenooClient("localhost", port=8069)
        print("✅ ZenooClient creation successful")

        # Test async context manager
        async with ZenooClient("localhost", port=8069) as client:
            print("✅ Async context manager works")

        return True

    except Exception as e:
        print(f"❌ Client creation failed: {e}")
        traceback.print_exc()
        return False

async def test_model_access():
    """Test model access without actual Odoo connection."""
    print("\n🧪 Testing model access...")

    try:
        from zenoo_rpc import ZenooClient
        from zenoo_rpc.models.common import ResPartner

        # Test if we can create client and access model method
        client = ZenooClient("localhost", port=8069)

        # This should work without connection
        model_query = client.model(ResPartner)
        print("✅ Model access successful")

        # Test query builder methods
        query = model_query.filter(is_company=True)
        print("✅ Filter method works")

        query = query.limit(10)
        print("✅ Limit method works")

        return True

    except Exception as e:
        print(f"❌ Model access failed: {e}")
        traceback.print_exc()
        return False

async def test_transaction_context():
    """Test transaction context manager."""
    print("\n🧪 Testing transaction context...")

    try:
        from zenoo_rpc import ZenooClient

        client = ZenooClient("localhost", port=8069)

        # Test if transaction method exists
        if hasattr(client, 'transaction'):
            print("✅ Transaction method exists")
        else:
            print("❌ Transaction method missing")
            return False

        return True

    except Exception as e:
        print(f"❌ Transaction context failed: {e}")
        traceback.print_exc()
        return False


async def test_batch_operations():
    """Test batch operations."""
    print("\n🧪 Testing batch operations...")

    try:
        from zenoo_rpc import ZenooClient

        client = ZenooClient("localhost", port=8069)

        # Test if batch method exists
        if hasattr(client, 'batch'):
            print("✅ Batch method exists")
        else:
            print("❌ Batch method missing")
            return False

        return True

    except Exception as e:
        print(f"❌ Batch operations failed: {e}")
        traceback.print_exc()
        return False


async def test_cache_setup():
    """Test cache setup methods."""
    print("\n🧪 Testing cache setup...")

    try:
        from zenoo_rpc import ZenooClient

        client = ZenooClient("localhost", port=8069)

        # Test if cache setup methods exist
        if hasattr(client, 'setup_cache_manager'):
            print("✅ setup_cache_manager method exists")
        else:
            print("❌ setup_cache_manager method missing")
            return False

        return True

    except Exception as e:
        print(f"❌ Cache setup failed: {e}")
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    print("🚀 Testing README.md code examples...\n")
    
    tests = [
        ("Imports", test_imports),
        ("Basic Client Creation", test_basic_client_creation),
        ("Model Access", test_model_access),
        ("Transaction Context", test_transaction_context),
        ("Batch Operations", test_batch_operations),
        ("Cache Setup", test_cache_setup),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST RESULTS SUMMARY")
    print("="*50)
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n📈 Total: {len(results)} tests")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    
    if failed > 0:
        print(f"\n⚠️  {failed} tests failed. README.md examples need fixes!")
        return 1
    else:
        print(f"\n🎉 All tests passed! README.md examples are working!")
        return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
