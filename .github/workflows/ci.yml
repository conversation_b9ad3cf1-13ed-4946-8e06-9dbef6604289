name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ["3.8", "3.9", "3.10", "3.11", "3.12"]
        exclude:
          # Reduce matrix size for faster CI
          - os: windows-latest
            python-version: "3.8"
          - os: windows-latest
            python-version: "3.9"
          - os: macos-latest
            python-version: "3.8"
          - os: macos-latest
            python-version: "3.9"

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/pyproject.toml') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev,redis]"

    - name: Set up Redis
      if: matrix.os == 'ubuntu-latest'
      uses: supercharge/redis-github-action@1.7.0
      with:
        redis-version: 7

    - name: Lint with ruff
      run: |
        ruff check src/ tests/ examples/
        ruff format --check src/ tests/ examples/

    - name: Type check with mypy
      run: |
        mypy src/zenoo_rpc

    - name: Test with pytest
      run: |
        pytest tests/ --cov=src/zenoo_rpc --cov-report=xml --cov-report=term-missing

    - name: Upload coverage to Codecov
      if: matrix.os == 'ubuntu-latest' && matrix.python-version == '3.11'
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install bandit[toml] safety

    - name: Run security checks with bandit
      run: |
        bandit -r src/ -f json -o bandit-report.json || true
        bandit -r src/

    - name: Check dependencies for known vulnerabilities
      run: |
        safety check --json --output safety-report.json || true
        safety check

  docs:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[docs]"

    - name: Build documentation
      run: |
        mkdocs build --strict

    - name: Test documentation links
      run: |
        # Add link checking if needed
        echo "Documentation built successfully"
