#!/usr/bin/env python3
"""
Final comprehensive test for Zenoo RPC 0.1.1
Testing core functionality that should work with any Odoo server
"""

import asyncio
from zenoo_rpc import ZenooClient

async def main():
    print("🚀 Zenoo RPC 0.1.1 - Final Verification Test")
    print("=" * 60)
    
    # Test configuration
    config = {
        "url": "https://odoo18.bestmix.one",
        "database": "bestmix_27_6", 
        "username": "tuan.le",
        "password": "drb6mtw3bah8byu*VEV"
    }
    
    try:
        async with ZenooClient(config["url"]) as client:
            print("🔗 Connecting to Odoo server...")
            
            # Test 1: Authentication
            await client.login(
                database=config["database"],
                username=config["username"],
                password=config["password"]
            )
            print(f"✅ Authentication successful!")
            print(f"   User ID: {client.uid}")
            print(f"   Database: {client.database}")
            
            # Test 2: Server Information
            version = await client.get_server_version()
            print(f"✅ Server version: {version['server_version']}")
            
            # Test 3: User Information
            user_info = await client.execute("res.users", "read", [client.uid], ["name", "login", "email"])
            user = user_info[0]
            print(f"✅ Connected as: {user['name']} ({user['login']})")
            if user.get('email'):
                print(f"   Email: {user['email']}")
            
            # Test 4: Search and Read (Safe operation)
            print("\n📊 Testing search and read operations...")
            
            # Count total partners
            total_partners = await client.search_count("res.partner", [])
            print(f"✅ Total partners in system: {total_partners}")
            
            # Search company partners
            companies = await client.search_read("res.partner", [
                ["is_company", "=", True]
            ], ["name", "email", "phone"], limit=3)
            print(f"✅ Found {len(companies)} company partners:")
            
            for i, company in enumerate(companies, 1):
                print(f"   {i}. {company['name']}")
                if company.get('email'):
                    print(f"      Email: {company['email']}")
                if company.get('phone'):
                    print(f"      Phone: {company['phone']}")
            
            # Test 5: Model Information
            print("\n🏗️ Testing model operations...")
            
            # Get partner model fields
            fields = await client.get_model_fields("res.partner")
            print(f"✅ res.partner model has {len(fields)} fields")
            
            # Show some important fields
            important_fields = ['name', 'email', 'phone', 'is_company', 'customer_rank', 'supplier_rank']
            available_fields = [f for f in important_fields if f in fields]
            print(f"✅ Available important fields: {', '.join(available_fields)}")
            
            # Test 6: Access Rights
            read_access = await client.check_model_access("res.partner", "read")
            print(f"✅ Read access to res.partner: {read_access}")
            
            # Test 7: Health Check
            health = await client.health_check()
            print(f"✅ Client health check: {health}")
            
            print("\n" + "=" * 60)
            print("🎉 ALL TESTS PASSED!")
            print("✅ Zenoo RPC 0.1.1 is working correctly!")
            print("✅ Connection, authentication, and data access all functional")
            print("✅ Package is ready for production use!")
            
            # Test 8: Show package info
            import zenoo_rpc
            print(f"\n📦 Package Information:")
            print(f"   Version: {zenoo_rpc.__version__}")
            print(f"   Author: {zenoo_rpc.__author__}")
            print(f"   Email: {zenoo_rpc.__email__}")
            
            return True
            
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        print(f"   Error type: {type(e).__name__}")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        if success:
            print("\n🎯 CONCLUSION: Zenoo RPC 0.1.1 is working perfectly!")
            print("   Users can install and use it with: pip install zenoo-rpc==0.1.1")
        else:
            print("\n⚠️ Some issues detected, but core functionality may still work")
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
