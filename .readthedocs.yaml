# Read the Docs configuration file for Zenoo RPC
# See https://docs.readthedocs.io/en/stable/config-file/v2.html for details

version: 2

# Set the OS, Python version and other tools you might need
build:
  os: ubuntu-22.04
  tools:
    python: "3.11"
  jobs:
    post_create_environment:
      # Install poetry
      - pip install poetry
    post_install:
      # Install dependencies with poetry
      - poetry config virtualenvs.create false
      - poetry install --with docs

# Build documentation in the "docs/" directory with MkDocs
mkdocs:
  configuration: mkdocs.yml
  fail_on_warning: false

# Optional but recommended, declare the Python requirements required
# to build your documentation
# See https://docs.readthedocs.io/en/stable/guides/reproducible-builds.html
python:
  install:
    - method: pip
      path: .
      extra_requirements:
        - docs
    - requirements: docs/requirements.txt
